"use client";
import Image from "next/image";
import { motion } from "motion/react";
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { TypingAnimation } from "@/components/ui/common/typingtextanimation";
import { ArrowRight } from "lucide-react";
import { resetResolver, Inputs } from "@/types/auth/resetpassword";
import Link from "next/link";
import ContentAnimation from "@/components/ui/auth/contentanimation";

const ResetPassword = () => {
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // React Form Hooks
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<Inputs>({
    resolver: resetResolver,
    defaultValues: {
      password: "",
      confirmPassword: "",
    },
  });

  // Validatation for react hooks using ZOD
  const onSubmit = async (data: Inputs) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 5000));
      console.log("data:", data);
      reset();
    } catch (error) {
      console.error("Login failed:", error);
    }
  };

  // Helper function to get error message
  const getErrorMessage = (fieldName: keyof Inputs) => {
    return errors[fieldName]?.message;
  };

  //   Show Password Handler
  const showPasswordHandler = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    console.log("button clicked");
    setShowPassword(!showPassword);
  };
  return (
    <div className="relative flex items-center justify-center gap-8">
      {/* Background Image */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "rgba(0, 0, 0, 0.3)",
        }}
      >
        <Image
          src="/auth/bgImage.jpg"
          alt="Signup Wall"
          // width={1440}
          // height={827}
          fill
          className="bg-[#0000004D] object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/60"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 mt-14 w-full items-center justify-between px-8 pt-14 md:mt-10 md:flex lg:mt-8">
        {/* Left Side - Welcome Text */}
        <ContentAnimation />

        {/* Right Side - Sign Up Form */}
        <div
          style={{
            background: "rgba(0, 0, 0, 0.8)",
            backdropFilter: "blur(4px) saturate(110%)",
            WebkitBackdropFilter: "blur(4px) saturate(110%)",
            boxShadow:
              "inset 0 1px 0 rgba(255, 255, 255, 0.18), 0 2px 10px rgba(0, 0, 0, 1)",
          }}
          className="my-4 h-[587px] max-w-[440] overflow-hidden rounded-xl border border-[var(--dark-primary-border)] px-6 py-8 shadow-md md:w-[440px]"
        >
          <div className="mb-14 text-center">
            <h2 className="mb-4 text-2xl font-semibold text-white">
              Forgot your password?
            </h2>
            <p className="text-sm text-gray-400">
              No worries. Enter your email or phone and we'll send you a reset
              link.
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <div>
                <label
                  htmlFor="password"
                  className="text-sm font-normal text-[var(--dark-text-muted)]"
                >
                  New Password
                </label>
                <div className="relative">
                  <Image
                    src="/auth/pass.svg"
                    alt="password icon"
                    width={20}
                    height={20}
                    className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                  />

                  <input
                    suppressHydrationWarning
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    id="password"
                    placeholder="Enter New Password"
                    className={`w-full rounded-lg border py-3 pr-12 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                      errors.password
                        ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                        : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                    }`}
                  />

                  <button
                    type="button"
                    onClick={showPasswordHandler}
                    className="cursor-pointer"
                  >
                    {showPassword ? (
                      <Image
                        src="/auth/eyeclose.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    ) : (
                      <Image
                        src="/auth/eyeopen.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-xs text-[var(--color-error)]">
                    {getErrorMessage("password")}
                  </p>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-3">
              <div>
                <label
                  htmlFor="password"
                  className="text-sm font-normal text-[var(--dark-text-muted)]"
                >
                  Confirm Password
                </label>
                <div className="relative">
                  <Image
                    src="/auth/pass.svg"
                    alt="password icon"
                    width={20}
                    height={20}
                    className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                  />

                  <input
                    suppressHydrationWarning
                    {...register("confirmPassword")}
                    type={showPassword ? "text" : "password"}
                    id="confirmPassword"
                    placeholder="Confirm your password"
                    className={`w-full rounded-lg border py-3 pr-12 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                      errors.confirmPassword
                        ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                        : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                    }`}
                  />

                  <button
                    type="button"
                    onClick={showPasswordHandler}
                    className="cursor-pointer"
                  >
                    {showPassword ? (
                      <Image
                        src="/auth/eyeclose.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    ) : (
                      <Image
                        src="/auth/eyeopen.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-xs text-[var(--color-error)]">
                    {getErrorMessage("confirmPassword")}
                  </p>
                )}
              </div>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`flex w-full items-center justify-center gap-2 rounded-lg bg-[var(--color-company)] px-4 py-3 text-center text-base font-medium text-[var(--dark-text)] transition duration-200 hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50`}
            >
              <Image
                src="/auth/people.svg"
                alt="login icon"
                width={15}
                height={15}
                className="h-auto w-4 space-x-3"
              />
              {!isSubmitting ? " Send Link" : "Submitting..."}
            </button>

            <Link href="/login">
              <button
                type="button"
                className={`flex w-full cursor-pointer items-center justify-center gap-2 rounded-lg px-4 py-3 text-center text-base font-medium text-[var(--dark-text)] transition duration-200 hover:bg-[var(--dark-surface)] disabled:cursor-not-allowed disabled:opacity-50`}
              >
                Login <ArrowRight className="h-6 w-6" />
              </button>
            </Link>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
