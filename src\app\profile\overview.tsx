"use client";
import StrategyNotes from "@/components/ui/profile/strategynotes";
import { cardsProps, overviewAchivementsProps } from "@/types/profile";
import Image from "next/image";

const OverView = () => {
  const statcard = [
    { label: "Followers", value: "1,248", icon: "/profile/followers.svg" },
    {
      label: "Portfolio Value",
      value: "Rs.17,103,224.88",
      icon: "/profile/graphup.svg",
    },
    { label: "Reputation", value: "752", icon: "/profile/star.svg" },
    { label: "Achievements", value: "28", icon: "/profile/achivement.svg" },
  ];

  const quickActions = [
    { label: "Save Strategy", icon: "/profile/bookmark.svg" },
    { label: "Share Portfolio ", icon: "/profile/upload.svg" },
    { label: "Run Analysis", icon: "/profile/growth.svg" },
  ];

  const recentActivities = [
    {
      name: "<PERSON>",
      description: "Commented on your portfolio strategy",
      time: "2 hours ago",
      icon: "/profile/profileUser2.svg",
    },
    {
      name: "<PERSON>",
      description: "Commented on your portfolio strategy",
      time: "2 hours ago",
      icon: "/profile/profileUser2.svg",
    },
    {
      name: "<PERSON>",
      description: "Commented on your portfolio strategy",
      time: "2 hours ago",
      icon: "/profile/profileUser2.svg",
    },
  ];

  const achievementsList = [
    {
      title: "Achievements",
      description: "Achieved 20% portfolio growth in a single quarter",
      date: "Poush 2082",
      icon: "/profile/award.svg",
      isImportaint: true,
    },
    {
      title: "Achievements",
      description: "Achieved 20% portfolio growth in a single quarter",
      date: "Kartik 2082",
      icon: "/profile/network.svg",
      isImportaint: false,
    },
    {
      title: "Risk Manager",
      description: "Maintained positive returns during market downturn",
      date: "Bhadra 2082",
      icon: "/profile/risk-management.svg",
      isImportaint: true,
    },
    {
      title: "Community Leader",
      description: "Helped 5+ community members with portfolio advice",
      date: "Shrawn 2082",
      icon: "/profile/badge.svg",
      isImportaint: false,
    },
    {
      title: "Quick Starter",
      description: "Complete all onboarding steps within 24 hours",
      date: "Baisakh 2082",
      icon: "/profile/strike.svg",
      isImportaint: false,
    },
  ];

  return (
    <div
      className="relative mx-auto flex w-full max-w-7xl flex-wrap justify-center gap-8 p-4 lg:flex-nowrap lg:p-8"
      style={{ backgroundColor: "var(--dark-bg)" }}
    >
      {/* Main Content Column */}
      <main className="w-full flex-shrink-0 lg:w-8/12">
        <h1 className="mb-4 text-xl font-semibold">OverView</h1>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {statcard.map((card, idx) => (
            <StatCards key={idx} {...card} />
          ))}
        </div>

        {/* Recent Activity & Quick Actions */}
        <div className="mt-6 grid grid-cols-1 gap-6 md:grid-cols-2">
          {/* Recent Activity */}
          <section className="h-fit rounded-lg bg-[var(--dark-surface)] p-4">
            <h1 className="text-base font-medium text-[var(--dark-text)]">
              Recent Activity
            </h1>
            <div className="mt-2 flex flex-col gap-3">
              {recentActivities.map((action, idx) => (
                <div
                  key={idx}
                  className="relative flex items-center justify-between gap-1 rounded-sm bg-[var(--dark-surface)] px-4 py-2"
                >
                  <Image
                    src={action.icon}
                    alt={action.name}
                    height={40}
                    width={40}
                  />
                  <div>
                    <p className="text-base font-normal text-[var(--dark-text)]">
                      {action.name}
                    </p>
                    <span className="absolute top-2 right-0 text-xs font-normal text-[var(--dark-text-muted)] lg:top-2 lg:right-23">
                      {action.time}
                    </span>
                    <span className="text-sm font-normal text-[var(--dark-text-muted)]">
                      {action.description}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </section>

          {/* Quick Actions */}
          <section className="h-fit rounded-lg bg-[var(--dark-surface)] p-4">
            <h1 className="text-base font-medium text-[var(--dark-text)]">
              Quick Actions
            </h1>
            <div className="mt-2 flex flex-col gap-3">
              {quickActions.map((action, idx) => (
                <div
                  key={idx}
                  className="flex items-center justify-between rounded-sm bg-[var(--dark-border)] px-4 py-2"
                >
                  <p className="text-base font-normal text-[var(--dark-text)]">
                    {action.label}
                  </p>
                  <Image
                    src={action.icon}
                    alt={action.label}
                    height={16}
                    width={16}
                  />
                </div>
              ))}
            </div>
          </section>
        </div>

        {/* Achievements */}
        <div>
          <div className="mt-6 flex items-center justify-between">
            <h1 className="mt-6 text-base font-medium text-[var(--dark-text)]">
              Achievements
            </h1>
            <button className="cursor-pointer text-sm font-normal text-[var(--dark-text)]">
              View All
            </button>
          </div>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-5">
            {achievementsList.map((achievement, idx) => (
              <AchievementsCards key={idx} {...achievement} />
            ))}
          </div>
        </div>
      </main>

      {/* Sidebar Column with Sticky Wrapper */}
      <aside className="w-full flex-shrink-0 lg:w-4/12">
        <div className="sticky top-20">
          <StrategyNotes />
        </div>
      </aside>
    </div>
  );
};

export default OverView;

const StatCards = ({ label, icon, value }: cardsProps) => {
  return (
    <div className="flex items-center justify-between rounded-md bg-[var(--dark-surface)] p-4">
      <Image src={icon} alt={label} width={44} height={44} />
      <div className="text-right">
        <h5 className="text-sm font-normal text-[var(--dark-text-muted)]">
          {label}
        </h5>
        <p className="text-base font-medium text-[var(--dark-text)]">{value}</p>
      </div>
    </div>
  );
};

const AchievementsCards = ({
  title,
  description,
  icon,
  date,
  isImportaint,
}: overviewAchivementsProps) => {
  return (
    <div
      className={`mt-6 flex flex-col items-center justify-between rounded-md border px-1 py-6 ${isImportaint ? "border-[#22C55E66] bg-gradient-to-b from-[#1F2937] to-[#14532D]" : "border-[var(--dark-border)] bg-[var(--dark-surface)]"}`}
    >
      <div
        className={`flex h-11 w-11 items-center justify-center rounded-full ${isImportaint ? "bg-[#22C55E66]" : "bg-[#A0A0A066]"}`}
      >
        <Image src={icon} alt={title} width={20} height={20} />
      </div>
      <div className="mt-3 text-center">
        <h5 className="text-sm font-normal text-[var(--dark-text)]">{title}</h5>
        <p className="mt-1 text-xs font-normal text-[#9CA3AF]">{description}</p>
        <span className="mt-2 text-xs font-normal text-[var(--dark-text-muted)]">
          {date}
        </span>
      </div>
    </div>
  );
};
