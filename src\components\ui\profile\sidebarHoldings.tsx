// components/Holdings.tsx

"use client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-icons/gr";

import { useState } from "react";
import { ChevronDown } from "lucide-react";
import { Holding } from "@/types/profile";

const SideBarHoldings = () => {
  const [openId, setOpenId] = useState<number | null>(null);
  const holdingsData: Holding[] = [
    {
      id: 1,
      name: "Nabil Bank Limited",
      ticker: "NABIL",
      shares: 12,
      currentValue: 162000,
      changePercent: 8.0,
      avgPrice: 180,
    },
    {
      id: 2,
      name: "Himalayan Bank Limited",
      ticker: "HBL",
      shares: 15,
      currentValue: 250000,
      changePercent: -2.5,
      avgPrice: 200,
    },
    {
      id: 3,
      name: "Nepal Investment Bank Ltd.",
      ticker: "NIBL",
      shares: 20,
      currentValue: 310000,
      changePercent: 5.2,
      avgPrice: 150,
    },
    {
      id: 4,
      name: "Standard Chartered Bank Ltd.",
      ticker: "SCB",
      shares: 8,
      currentValue: 190000,
      changePercent: 1.8,
      avgPrice: 210,
    },
    {
      id: 5,
      name: "Everest Bank Limited",
      ticker: "EBL",
      shares: 18,
      currentValue: 220000,
      changePercent: -0.5,
      avgPrice: 160,
    },
  ];

  //   Handle toggle function to expand/collapse when buttons clicked
  const handleToggle = (id: number) => {
    setOpenId(openId === id ? null : id);
  };

  return (
    <div className="w-full rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] p-4">
      <h1 className="text-base font-medium">Holdings</h1>

      {/* Stock Cards Here */}
      <div className="mt-4">
        {holdingsData.map((holdings) => (
          <div
            key={holdings.id}
            className="mt-4 rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)]"
          >
            <button
              type="button"
              onClick={() => handleToggle(holdings.id)}
              className="flex w-full cursor-pointer items-center gap-2.5 p-2.5"
            >
              {/* Stock Short Form Name */}
              <span className="rounded-lg bg-[#4B5563] px-3 py-2.5 text-base font-normal">
                {holdings.ticker}
              </span>

              {/* Top Name Information  */}
              <div className="flex w-full items-center justify-between gap-4">
                <div className="flex-1 text-start text-wrap">
                  <h5 className="text-sm font-normal">{holdings.name}</h5>
                  <p className="text-xs text-[var(--dark-text-muted)]">
                    {holdings.shares} Shares
                  </p>
                </div>

                {/* Additional Information */}
                <div>
                  <h2 className="text-base font-medium">
                    Rs.{holdings.currentValue}
                  </h2>
                  <div className="flex w-full items-center justify-between">
                    <p
                      className={`flex items-center gap-1.5 text-xs ${
                        holdings.changePercent >= 0
                          ? "text-[var(--color-success)]"
                          : "text-[var(--color-error)]"
                      }`}
                    >
                      <GrLineChart /> {holdings.changePercent}%
                    </p>

                    <ChevronDown
                      className={`h-4 w-4 transition-transform ${openId === holdings.id ? "rotate-180" : ""}`}
                    />
                  </div>
                </div>
              </div>
            </button>

            {/* // When Clicked, show more details */}
            {openId === holdings.id && (
              <div className="p-2.5">
                <div className="flex justify-between text-sm">
                  <span>Avg Price:</span>
                  <span>Rs.{holdings.avgPrice} per share</span>
                </div>
                <div className="mt-2 flex justify-between text-sm">
                  <span>Current Value:</span>
                  <span>Rs.{holdings.currentValue.toLocaleString()}</span>
                </div>
                <div className="mt-4 flex h-24 items-center justify-center rounded-lg bg-gray-700 underline">
                  Chart Here
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SideBarHoldings;
