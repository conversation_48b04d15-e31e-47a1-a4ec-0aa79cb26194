import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

// +++++++++++++++++++++++ Zod Schema for Validation ++++++++++++++++++++++++++++++++++++++//

export const registerSchema = z.object({
  fullName: z
    .string()
    .min(7, "Full name must be at least 7 characters")
    .max(50, "Full name must not exceed 50 characters")
    .regex(/^[a-zA-Z\s]+$/, "Full name can only contain letters and spaces"),

  email: z
    .string()
    .nonempty("email is required")
    .email("Please enter a valid email address"),

  password: z
    .string()
    .nonempty("Password is required")
    .min(6, "Password must be at least 6 character longer")
    .max(100, "Password must not exceed 100 character")
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      "Password must contain at least one uppercase letter, one lowercase letter, and one number",
    ),
});

export type Inputs = z.infer<typeof registerSchema>;

export const registerResolver = zodResolver(registerSchema);
