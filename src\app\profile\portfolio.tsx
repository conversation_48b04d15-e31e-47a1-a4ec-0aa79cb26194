import {
  PortfolioAchievements,
  PortfolioValue,
  SectorAllocation,
  TopGainersLosers,
} from "@/components/ui/profile/portfolioGraphs";
import SideBarHoldings from "@/components/ui/profile/sidebarHoldings";
import StrategyNotes from "@/components/ui/profile/strategynotes";
import { InformationCardProps } from "@/types/profile";
import { ArrowDown, ArrowUp } from "lucide-react";

const Portfolio = () => {
  const informationCards = [
    {
      title: "Total Value",
      value: "Rs. 1,245,678",
      change: "+2.4%",
      isProfit: true,
    },
    {
      title: "Day Change",
      value: "Rs. 12,450",
      change: "+1.2%",
      isProfit: true,
    },
    {
      title: "Total Profit",
      value: "Rs. 245,678",
      change: "+24.5%",
      isProfit: true,
    },
    {
      title: "Dividend Yield",
      value: "4.2%",
      change: "+2.4%",

      isProfit: false,
    },
  ];

  return (
    <>
      <div
        className="col-span-12 mt-32 mb-4 grid min-h-screen w-full grid-cols-12 gap-4 rounded-t-lg border border-[var(--dark-border)] p-4 md:mt-6"
        style={{ backgroundColor: "var(--dark-bg)" }}
      >
        {/* Main Content Section */}
        <div className="col-span-12 md:col-span-8">
          <div className="grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {informationCards.map((information, idx) => (
              <InformationCards key={idx} {...information} />
            ))}
          </div>

          {/* Sector Allocation & Portfolio Value */}
          <div className="mt-6 grid w-full grid-cols-1 gap-6 md:grid-cols-2">
            <SectorAllocation />
            <PortfolioValue />
          </div>

          {/* Top Gainers & Losers */}
          <div className="mt-6">
            <TopGainersLosers />
            <PortfolioAchievements />
          </div>
        </div>

        {/* Right Sidebar Section */}
        <div className="col-span-12 w-full md:col-span-4">
          {/* Added sticky positioning here */}
          <div className="sticky top-20 space-y-6">
            <SideBarHoldings />
            <StrategyNotes />
          </div>
        </div>
      </div>
    </>
  );
};

export default Portfolio;

export const InformationCards = ({
  isProfit,
  title,
  value,
  change,
}: InformationCardProps) => {
  return (
    <div className="min-w-[144px] rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] p-4">
      <h5 className="mb-1 text-sm font-normal text-[var(--dark-text-muted)]">
        {title}
      </h5>
      <h2 className="mb-1 text-xl font-medium text-[var(--dark-text)]">
        {value}
      </h2>
      <h4 className="flex items-center gap-1 text-base font-medium">
        {isProfit ? (
          <ArrowUp className="h-4 w-4 text-[var(--color-success)]" />
        ) : (
          <ArrowDown className="h-4 w-4 text-[var(--color-error)]" />
        )}
        <span
          className={`${
            isProfit
              ? "text-[var(--color-success)]"
              : "text-[var(--color-error)]"
          }`}
        >
          {change}
        </span>
      </h4>
    </div>
  );
};
