import {
  ChartNoAxesColumn,
  ChartSpline,
  Clock,
  TrendingDown,
  TrendingUp,
} from "lucide-react";

const KeyMetrics = () => {
  const MetricsList = [
    { label: "Volume", value: "Rs.1.52M", icon: ChartNoAxesColumn },
    { label: "Previous Close", value: "Rs.1,225.00", icon: Clock },
    { label: "Open Price", value: "Rs.1,230.50", icon: ChartSpline },
    {
      label: "Day's Range",
      value: "Rs.1,240 - Rs.1,255",
      icon: ChartNoAxesColumn,
    },
    { label: "52-Week Low", value: "Rs.1,050.00", icon: TrendingDown },
    { label: "52-Week High", value: "Rs.1,350.00", icon: TrendingUp },
  ];
  return (
    <>
      <h2 className="text-xl font-medium text-[var(--dark-text)]">
        Key <span className="text-[var(--color-company)]">Metrics</span>
      </h2>
      {/* Cards Elements */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3">
        {MetricsList.map((item, idx) => (
          <div key={idx}>
            <div className="p-3">
              <div className="rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] p-4">
                <p className="flex items-center gap-1 text-sm font-normal text-[var(--dark-text-muted)]">
                  <item.icon
                    className={`h-5 w-5 ${
                      item.icon === TrendingDown && "text-[var(--color-error)]"
                    } ${item.icon === TrendingUp && "text-[var(--color-success)]"}`}
                  />{" "}
                  {item.label}
                </p>
                <h5 className="mt-1 ml-1 text-base font-medium text-[var(--dark-text)]">
                  {item.value}
                </h5>
              </div>
            </div>
          </div>
        ))}
      </div>
    </>
  );
};

export default KeyMetrics;
