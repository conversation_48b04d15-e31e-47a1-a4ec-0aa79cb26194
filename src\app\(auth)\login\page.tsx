"use client";
import Image from "next/image";
import { motion } from "motion/react";
import React, { useState } from "react";
import { useForm } from "react-hook-form";
import Link from "next/link";
import { loginResolver, Inputs } from "@/types/auth/login";
import ContentAnimation from "@/components/ui/auth/contentanimation";

const Login = () => {
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // React Form Hooks
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<Inputs>({
    resolver: loginResolver,
    defaultValues: {
      email: "",
      password: "",
    },
  });

  // Validatation for react hooks using ZOD
  const onSubmit = async (data: Inputs) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 5000));
      console.log("data:", data);
      reset();
    } catch (error) {
      console.error("Login failed:", error);
    }
  };

  // Helper function to get error message
  const getErrorMessage = (fieldName: keyof Inputs) => {
    return errors[fieldName]?.message;
  };

  //   Show Password Handler
  const showPasswordHandler = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    console.log("button clicked");
    setShowPassword(!showPassword);
  };
  return (
    <div className="relative flex items-center justify-center gap-8">
      {/* Background Image */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "rgba(0, 0, 0, 0.3)",
        }}
      >
        <Image
          src="/auth/bgImage.jpg"
          alt="Signup Wall"
          // width={1440}
          // height={827}
          fill
          className="bg-[#0000004D] object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/60"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 mt-14 w-full items-center justify-between px-8 pt-14 md:mt-10 md:flex lg:mt-8">
        {/* Left Side - Welcome Text */}
        <ContentAnimation />

        {/* Right Side - Sign Up Form */}
        <div
          style={{
            background: "rgba(0, 0, 0, 0.8)",
            backdropFilter: "blur(4px) saturate(110%)",
            WebkitBackdropFilter: "blur(4px) saturate(110%)",
            boxShadow:
              "inset 0 1px 0 rgba(255, 255, 255, 0.18), 0 2px 10px rgba(0, 0, 0, 1)",
          }}
          className="my-4 h-[587px] max-w-[440] overflow-hidden rounded-xl border border-[var(--dark-primary-border)] px-6 py-8 shadow-md md:w-[440px]"
        >
          <div className="mb-6 text-center">
            <h2 className="mb-2 text-2xl font-semibold text-white"> Login</h2>
            <p className="text-sm text-gray-400">
              Log in to continue tracking NEPSE
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="text-sm font-normal text-[var(--dark-text-muted)]"
              >
                Email or Phone Number
              </label>
              <div className="relative">
                <Image
                  src="/auth/email.svg"
                  alt="email icon"
                  width={20}
                  height={20}
                  className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                />
                <input
                  suppressHydrationWarning
                  {...register("email")}
                  type="email"
                  placeholder="Enter your email or phone"
                  className={`w-full rounded-lg border px-4 py-3 pr-4 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                    errors.email
                      ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                      : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                  }`}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-xs text-[var(--color-error)]">
                  {getErrorMessage("email")}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 gap-3">
              <div>
                <label
                  htmlFor="password"
                  className="text-sm font-normal text-[var(--dark-text-muted)]"
                >
                  Password
                </label>
                <div className="relative">
                  <Image
                    src="/auth/pass.svg"
                    alt="password icon"
                    width={20}
                    height={20}
                    className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                  />

                  <input
                    suppressHydrationWarning
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    id="password"
                    placeholder="Enter your password"
                    className={`w-full rounded-lg border py-3 pr-12 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                      errors.password
                        ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                        : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                    }`}
                  />

                  <button
                    type="button"
                    onClick={showPasswordHandler}
                    className="cursor-pointer"
                  >
                    {showPassword ? (
                      <Image
                        src="/auth/eyeclose.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    ) : (
                      <Image
                        src="/auth/eyeopen.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-xs text-[var(--color-error)]">
                    {getErrorMessage("password")}
                  </p>
                )}
              </div>
            </div>

            <div className="mb-4 text-end text-xs font-normal text-[#1A7EF2]">
              <Link href={"/forgetpassword"}>Forget Password?</Link>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`flex w-full items-center justify-center gap-2 rounded-lg bg-[var(--color-company)] px-4 py-3 text-center text-base font-medium text-[var(--dark-text)] transition duration-200 hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50`}
            >
              <Image
                src="/auth/people.svg"
                alt="login icon"
                width={15}
                height={15}
                className="h-auto w-4 space-x-3"
              />
              {!isSubmitting ? " Login" : "Submitting..."}
            </button>
          </form>

          <div className="mt-6 flex w-full flex-col items-center justify-center text-center">
            <span className="mb-2 text-base font-normal text-[var(--dark-text-muted)]">
              OR,
            </span>
            <motion.button
              initial={{ width: 40 }}
              animate={{
                width: isHovered ? 402 : 40,
                padding: isHovered ? "0 8px" : "0",
                scale: isHovered ? 1.05 : 1,
                borderRadius: isHovered ? "5px" : "50%",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{
                width: {
                  type: "spring",
                  duration: 0.6,
                  stiffness: 260,
                  damping: 20,
                  ease: "easeInOut",
                },
                duration: 0.9,
                ease: "easeInOut",
                type: "spring",
                stiffness: 260,
                damping: 20,
              }}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              className="relative flex h-10 items-center space-x-4 overflow-hidden bg-[#EDEDED] shadow-md transition-shadow duration-300 hover:shadow-lg"
              aria-label="Continue with Google"
            >
              {/* Google Icon */}
              <div className="ml-2 flex-shrink-0">
                <Image
                  src="/auth/google.svg"
                  alt="google icon"
                  height={24}
                  width={24}
                  className="h-auto w-6 transition-all duration-300 group-hover:brightness-110"
                />
              </div>

              {/* Text that appears on hover */}
              <motion.span
                className="mr-2 overflow-hidden text-[16px] font-[400] whitespace-nowrap text-[#A0A0A0]"
                initial={{ opacity: 0, width: 0 }}
                animate={{
                  opacity: isHovered ? 1 : 0,
                  width: isHovered ? "auto" : 0,
                }}
                transition={{
                  duration: 0.4,

                  ease: "easeOut",
                }}
              >
                Continue with Google
              </motion.span>
            </motion.button>

            <p className="mt-4 text-sm font-medium text-[var(--dark-text)]">
              Don't have a account?{" "}
              <Link href="/signup">
                <span className="text-[var(--color-company)]">Sign Up</span>
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
