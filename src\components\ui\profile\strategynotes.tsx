import Image from "next/image";
import { useState } from "react";

const StrategyNotes = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  const strategyNotesData = [
    {
      title: "Hold TSLA until Q3 earnings",
      tags: ["Strategy", "Tech"],
    },
    {
      title: "Review AAPL quarterly report",
      tags: ["Research"],
    },
    {
      title: "Set stop-loss for NVDA position",
      tags: ["Risk-management"],
    },
  ];

  return (
    <aside className="col-span-12 md:col-span-4">
      <div
        className="grid gap-4 rounded-lg p-3"
        style={{
          backgroundColor: "var(--dark-surface)",
        }}
      >
        <h1 className="text-base font-medium text-[var(--dark-text)]">
          Strategy Notes
        </h1>

        {strategyNotesData.map((note, index) => (
          <div
            key={index}
            onClick={() => setActiveIndex(index)}
            className={`flex cursor-pointer items-start justify-between rounded-sm p-3 ${
              activeIndex === index
                ? "bg-[#2563EB33]"
                : "bg-[var(--dark-border)]"
            }`}
          >
            <div>
              <h5 className="mb-2 text-sm font-normal text-[var(--dark-text-muted)]">
                {note.title}
              </h5>
              <div className="flex flex-wrap gap-1.5">
                {note.tags.map((tag, tagIdx) => (
                  <span
                    key={tagIdx}
                    className="rounded-xs bg-[var(--dark-border)] px-1.5 py-0.5 text-xs font-normal"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>

            {activeIndex === index && (
              <Image
                src="/profile/pin.svg"
                alt="Pinned Note"
                width={16}
                height={16}
                className="flex-shrink-0"
              />
            )}
          </div>
        ))}

        <button className="h-[44px] w-full rounded-sm bg-[#1F2937CC] p-2.5 font-medium text-[var(--dark-text)]">
          + Login
        </button>
      </div>
    </aside>
  );
};

export default StrategyNotes;
