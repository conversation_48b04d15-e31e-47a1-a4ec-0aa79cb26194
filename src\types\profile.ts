export type Tab = {
  id: string;
  label: string;
};

export interface cardsProps {
  label: string;
  value: string;
  icon: string;
}

export interface overviewAchivementsProps {
  title: string;
  description: string;
  icon: string;
  date: string;
  isImportaint: boolean;
}

export interface InformationCardProps {
  title: string;
  value: string;
  change: string;
  isProfit: boolean;
}

export interface Holding {
  id: number;
  name: string;
  ticker: string;
  shares: number;
  currentValue: number;
  changePercent: number;
  avgPrice: number;
}
