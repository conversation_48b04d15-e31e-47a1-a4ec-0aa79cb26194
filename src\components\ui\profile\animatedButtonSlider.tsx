"use client";
import { Tab } from "@/types/profile";
import { motion } from "motion/react";

interface AnimatedButtonSliderProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const AnimatedButtonSlider = ({
  activeTab,
  onTabChange,
}: AnimatedButtonSliderProps) => {
  const tabs: Tab[] = [
    { id: "overview", label: "Overview" },
    { id: "portfolio", label: "Portfolio" },
  ];

  return (
    <div className="flex space-x-1 rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] px-2 py-1">
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`${
            activeTab === tab.id ? "" : "hover:text-white/60"
          } relative rounded-full px-3 py-1.5 text-sm font-medium text-white transition focus-visible:outline-2`}
          style={{
            WebkitTapHighlightColor: "transparent",
          }}
        >
          {activeTab === tab.id && (
            <motion.span
              layoutId="bubble"
              className="absolute inset-0 z-10 rounded-[6px] bg-[#04070C] mix-blend-difference"
              style={{ borderRadius: 6 }}
              transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
            />
          )}
          <span
            className={`${activeTab === tab.id ? "text-[var(--color-company)]" : "text-[var(--dark-text-muted)]"}`}
          >
            {tab.label}
          </span>
        </button>
      ))}
    </div>
  );
};

export default AnimatedButtonSlider;
