import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

export const resetSchema = z
  .object({
    password: z
      .string()
      .nonempty("Password is required")
      .min(6, "Password must be at least 6 character longer")
      .max(100, "Password must not exceed 100 character")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number",
      ),
    confirmPassword: z
      .string()
      .nonempty("Confirm Password is required")
      .min(6, "Password must be at least 6 character longer")
      .max(100, "Password must not exceed 100 character"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export type Inputs = z.infer<typeof resetSchema>;

export const resetResolver = zodResolver(resetSchema);
