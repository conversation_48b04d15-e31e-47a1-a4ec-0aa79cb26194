"use client";
import { Container } from "@/components/ui/common/container";
import Image from "next/image";
import AnimatedButtonSlider from "@/components/ui/profile/animatedButtonSlider";
import { useState } from "react";
import OverView from "./overview";
import Portfolio from "./portfolio";
import { motion, AnimatePresence } from "motion/react";
import Link from "next/link";

const Profile = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const [isNotifications, setIsNotifications] = useState<boolean>(true);

  // Function to handle tab changes, passed to the slider
  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
  };
  return (
    <Container className="mt-20 h-full min-h-screen w-full text-[var(--dark-text)] md:mt-26">
      <div className="mb-20 flex h-[72px] w-full flex-col items-center justify-between gap-6 px-4 py-3 md:mb-1 md:flex-row">
        {/* Left Side Container with logic to switch behaviour according to button switches from "overview" to "portfolio" */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
        >
          {activeTab === "overview" ? (
            <div className="flex items-center gap-3">
              {/* Profile Image Section */}
              <div className="relative">
                <Image
                  src="/profile/profileUser.svg"
                  alt="User Profile Picture"
                  height={48}
                  width={48}
                  className="rounded-full border border-[var(--color-company)]"
                />
                <p className="absolute -right-2 -bottom-1 flex h-5 w-9 items-center justify-center rounded-sm border border-[var(--color-company)] bg-[var(--color-company)] px-2.5 py-1 text-xs font-normal text-[var(--dark-text)]">
                  Pro
                </p>
              </div>

              {/* Users Personal Details */}
              <div className="ml-3 md:ml-0">
                <h1 className="text-base font-medium text-[var(--dark-text)]">
                  Alex Thompson
                </h1>
                <p className="text-sm text-[var(--dark-text-muted)]">
                  @alextrader • Member since 2020
                </p>
              </div>
              <button
                className="flex h-[32px] w-[32px] cursor-pointer items-center justify-center"
                onClick={() => alert("Edit Profile Clicked")}
              >
                <Image
                  src="/profile/edit.svg"
                  alt="User Profile Picture"
                  height={16}
                  width={16}
                  className="h-auto w-auto"
                />
              </button>
            </div>
          ) : (
            <div className="my-2 flex flex-col gap-1 text-center md:text-left">
              <h1 className="text-2xl font-semibold text-[#FFFAFA]">
                NEPSE Portfolio Dashboard
              </h1>
              <p className="text-base font-normal text-[var(--dark-text-muted)]">
                Last updated: Shrawn 28, 2023 | NEPSE: 2,145.67 (+15.45)
              </p>
            </div>
          )}
        </motion.div>

        {/* Right Side Actions */}
        <section className="flex items-center gap-3">
          {/*  Amimated Slider Button*/}
          <AnimatedButtonSlider
            activeTab={activeTab}
            onTabChange={handleTabChange}
          />

          {/* Notification Button */}
          <button className="relative flex h-9 w-9 cursor-pointer items-center justify-center">
            {isNotifications && (
              <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-[var(--color-success)]" />
            )}

            <Image
              src="/profile/bellNotification.svg"
              alt="Notification"
              height={20}
              width={20}
              className="h-auto w-auto"
            />
          </button>

          {/* Settings Button */}
          <Link href={"/settings"}>
            <button className="relative flex h-9 w-9 cursor-pointer items-center justify-center">
              <Image
                src="/profile/gears.svg"
                alt="Settings"
                height={20}
                width={20}
                className="h-auto w-auto"
              />
            </button>
          </Link>
        </section>
      </div>

      <Container>
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {activeTab === "overview" ? <OverView /> : <Portfolio />}
          </motion.div>
        </AnimatePresence>
      </Container>
    </Container>
  );
};

export default Profile;
