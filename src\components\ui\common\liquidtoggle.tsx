"use client";

import React, { useState } from "react";

const Toggle = () => {
  const [isOn, setIsOn] = useState(false);

  return (
    <div
      onClick={() => setIsOn(!isOn)}
      className={`relative h-[24px] w-[44px] cursor-pointer rounded-full transition-colors duration-300 ${
        isOn ? "bg-gradient-to-r from-[#1A7EF2] to-[#6366F1]" : "bg-[#333]"
      }`}
    >
      <div
        className={`absolute top-[4px] h-[16px] w-[16px] rounded-full bg-white transition-all duration-300 ${
          isOn ? "left-[24px]" : "left-[4px]"
        } ${
          isOn
            ? "origin-right hover:scale-x-[1.6]"
            : "origin-left hover:scale-x-[1.6]"
        }`}
      ></div>
    </div>
  );
};

export default Toggle;
