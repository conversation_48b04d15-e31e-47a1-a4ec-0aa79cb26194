import { PrimaryButtonProps } from "@/types/buttons";
import Image from "next/image";

export const PrimaryButton = ({ label, icon }: PrimaryButtonProps) => {
  return (
    <div
      className={`flex items-center justify-between gap-2 rounded-sm bg-[var(--color-company)] px-2.5 py-2 text-base font-medium text-[var(--dark-text)]`}
    >
      <Image src={icon} alt={label} width={20} height={20} />
      {label}
    </div>
  );
};
