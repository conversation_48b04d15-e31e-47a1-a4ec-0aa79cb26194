"use client";
import { PrimaryButton } from "@/components/ui/common/buttons";
import { Container } from "@/components/ui/common/container";
import { Star } from "lucide-react";
import { Inter } from "next/font/google";
import Image from "next/image";
import { useState } from "react";
import KeyMetrics from "./keymetrics";
import CommunityPosts from "./communityposts";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});
const StockView = () => {
  const [isChartSelected, setIsChartSelected] = useState<Array<string>>([
    "Overview",
  ]);

  //   Chart Selection Buttons Lists
  const ChartSelectionButton = [
    "Overview",
    "News",
    "Ideas",
    "Discussion",
    "Technical",
    "Markets",
  ];

  

  const ChartSelectionHandler = (item: string) => {
    if (isChartSelected.includes(item)) {
      setIsChartSelected(isChartSelected.filter((i) => i !== item));
    } else {
      setIsChartSelected([...isChartSelected, item]);
    }
  };

  return (
    <Container className="col-span-12 mt-25 grid min-h-screen grid-cols-12 gap-4 px-4 py-3 sm:px-8">
      {/* Main Column Section - 9 columns */}
      <main className="col-span-12 lg:col-span-9">
        <header className="w-full">
          {/* Script Name */}
          <div className="flex w-full flex-row items-center justify-between gap-2">
            <h1 className="text-center text-xl font-semibold text-[var(--dark-text)] uppercase">
              NABIL{" "}
              <span
                className={`${inter.className} block text-lg font-bold text-[var(--dark-text-muted)] capitalize sm:inline`}
              >
                - Nabil Bank Ltd
              </span>
            </h1>
            <Star className="h-5 w-5 text-[var(--dark-text)]" />
          </div>

          {/* Script Price & Charts */}
          <div className="mt-2 flex flex-row items-center justify-between gap-3">
            <div className="text-center sm:text-left">
              <h1 className="text-lg font-semibold text-[var(--color-success)]">
                Rs.1,250.5
              </h1>
              <p className="sm:text-md text-sm font-normal text-[var(--color-success)]">
                +25.50 (+2.08%){" "}
                <span className="text-[var(--dark-text)]">Live</span>
              </p>
            </div>
            <button onClick={() => console.log("Add to Watchlist")}>
              <PrimaryButton label="Super Chart" icon="/stockview/chart.svg" />
            </button>
          </div>
        </header>

        {/* Chart View With Buttons Selections */}
        <section className="mt-10 sm:mt-20">
          {/* Buttons Selections Container  */}
          <div className="mb-4 flex flex-wrap items-center justify-center gap-2 sm:mb-6 sm:gap-4 md:justify-start">
            {ChartSelectionButton.map((item, idx) => (
              <button
                key={idx}
                type="button"
                onClick={() => ChartSelectionHandler(item)}
              >
                <ChartViewButton
                  label={item}
                  isChartSelected={isChartSelected}
                />
              </button>
            ))}
          </div>

          {/* Chart View Element Comes Over Here */}
          <div
            className="h-fit w-full overflow-hidden rounded-md border border-[var(--dark-border)] p-2 sm:p-3"
            style={{ background: "var(--dark-bg)" }}
          >
            <Image
              src="/stockview/live-chart.svg"
              alt="chart"
              width={866}
              height={532}
              className="h-auto w-full object-contain py-1.5"
            />
          </div>
        </section>

        {/* Key Metrics Section */}
        <section className="mt-12 pt-8 pb-14">
          <KeyMetrics />
        </section>

        {/* Community Posts with Drag Scroll */}
        <section className="mt-12 pt-8 pb-14">
          <h2 className="mb-4 text-base font-medium text-[var(--dark-text)]">
            <span className="text-[var(--color-company)]">Community Posts</span>{" "}
            for NABIL
          </h2>

          <CommunityPosts />
        </section>
      </main>

      {/* Sidebar Column - 3 columns */}
      <aside className="col-span-12 lg:col-span-3">
        <div className="rounded-lg border border-[var(--dark-border)] bg-[var(--dark-surface)] p-4">
          <h1 className="text-[var(--dark-text)]">Sidebar Content</h1>
          {/* Add your sidebar content here */}
        </div>
      </aside>
    </Container>
  );
};

export default StockView;

export const ChartViewButton = ({
  label,
  isChartSelected,
}: {
  label: string;
  isChartSelected: Array<string>;
}) => {
  return (
    <div
      className={`h-6 rounded-[20px] sm:h-7 ${
        isChartSelected.includes(label) ? "bg-[#2182F299]" : "bg-[#1F2937]"
      } px-2 py-1 text-center text-xs font-normal text-[var(--dark-text)] sm:px-2.5 sm:text-sm`}
    >
      {label}
    </div>
  );
};
