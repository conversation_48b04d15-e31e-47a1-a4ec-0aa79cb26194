"use client";
import Image from "next/image";
import { motion } from "motion/react";
import { useState } from "react";
import { Inputs, registerResolver } from "@/types/auth/register";
import { useForm } from "react-hook-form";
import Link from "next/link";
import ContentAnimation from "@/components/ui/auth/contentanimation";

const SignUp = () => {
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // React Form Hooks
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<Inputs>({
    resolver: registerResolver,
    defaultValues: {
      fullName: "",
      email: "",
      password: "",
    },
  });

  // Validatation for react hooks using ZOD
  const onSubmit = async (data: Inputs) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 5000));
      console.log("data:", data);
      reset();
    } catch (error) {
      console.error("Registration failed:", error);
    }
  };

  // Helper function to get error message
  const getErrorMessage = (fieldName: keyof Inputs) => {
    return errors[fieldName]?.message;
  };

  //   Show Password Handler
  const showPasswordHandler = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    console.log("button clicked");
    setShowPassword(!showPassword);
  };
  return (
    <div className="relative flex items-center justify-center gap-8">
      {/* Background Image */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: "rgba(0, 0, 0, 0.3)",
        }}
      >
        <Image
          src="/auth/bgImage.jpg"
          alt="Signup Wall"
          // width={1440}
          // height={827}
          fill
          className="bg-[#0000004D] object-cover"
          priority
        />
        <div className="absolute inset-0 bg-black/60"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 mt-14 w-full items-center justify-between px-8 pt-14 md:mt-10 md:flex lg:mt-8">
        {/* Left Side - Welcome Text */}
        <ContentAnimation />

        {/* Right Side - Sign Up Form */}
        <div
          // style={{
          //   background:
          //     "linear-gradient(0deg, rgba(255, 255, 255, 0.07)), linear-gradient(0deg, rgba(0, 0, 0, 0.5))",
          //   backdropFilter: "blur(18px)",
          // }}
          style={{
            background: "rgba(0, 0, 0, 0.8)",
            backdropFilter: "blur(4px) saturate(110%)",
            WebkitBackdropFilter: "blur(4px) saturate(110%)",
            boxShadow:
              "inset 0 1px 0 rgba(255, 255, 255, 0.18), 0 2px 10px rgba(0, 0, 0, 0.8)",
          }}
          className="my-4 max-w-[440] overflow-hidden rounded-xl border border-[var(--dark-primary-border)] px-6 py-8 shadow-md md:w-[440px]"
        >
          <div className="mb-6 text-center">
            <h2 className="mb-2 text-2xl font-semibold text-white">Sign Up</h2>
            <p className="text-sm text-gray-400">
              Start trading smarter with NEP X
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <div>
                <label
                  htmlFor="fullName"
                  className="text-sm font-normal text-[var(--dark-text-muted)]"
                >
                  Full Name
                </label>
                <input
                  {...register("fullName")}
                  type="text"
                  placeholder="Enter your full name"
                  className={`w-full rounded-lg border px-4 py-3 text-[var(--dark-text)] focus:ring-1 focus:outline-none ${
                    errors.fullName
                      ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                      : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                  }`}
                />
                {errors.fullName && (
                  <p className="mt-1 text-xs text-[var(--color-error)]">
                    {getErrorMessage("fullName")}
                  </p>
                )}
              </div>
            </div>

            <div>
              <label
                htmlFor="email"
                className="text-sm font-normal text-[var(--dark-text-muted)]"
              >
                Email or Phone Number
              </label>
              <div className="relative">
                <Image
                  src="/auth/email.svg"
                  alt="email icon"
                  width={20}
                  height={20}
                  className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                />
                <input
                  {...register("email")}
                  type="email"
                  placeholder="Enter your email or phone"
                  className={`w-full rounded-lg border px-4 py-3 pr-4 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                    errors.email
                      ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                      : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                  }`}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-xs text-[var(--color-error)]">
                  {getErrorMessage("email")}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 gap-3">
              <div>
                <label
                  htmlFor="password"
                  className="text-sm font-normal text-[var(--dark-text-muted)]"
                >
                  Password
                </label>
                <div className="relative">
                  <Image
                    src="/auth/pass.svg"
                    alt="password icon"
                    width={20}
                    height={20}
                    className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                  />

                  <input
                    {...register("password")}
                    type={showPassword ? "text" : "password"}
                    id="password"
                    placeholder="Enter your password"
                    className={`w-full rounded-lg border py-3 pr-12 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                      errors.password
                        ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                        : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                    }`}
                  />

                  <button
                    type="button"
                    onClick={showPasswordHandler}
                    className="cursor-pointer"
                  >
                    {showPassword ? (
                      <Image
                        src="/auth/eyeclose.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    ) : (
                      <Image
                        src="/auth/eyeopen.svg"
                        alt="eye open icon"
                        width={18}
                        height={18}
                        className="absolute top-1/2 right-3 w-fit -translate-y-1/2 transform text-white"
                      />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-xs text-[var(--color-error)]">
                    {getErrorMessage("password")}
                  </p>
                )}
              </div>
            </div>

            <div className="mb-4 text-xs font-normal text-[var(--dark-text)]">
              You must agree to the{" "}
              <a
                href="#"
                className="text-[var(--color-company)] hover:underline"
              >
                Terms of Service
              </a>{" "}
              and{" "}
              <a
                href="#"
                className="text-[var(--color-company)] hover:underline"
              >
                Privacy Policy
              </a>
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className={`flex w-full items-center justify-center gap-2 rounded-lg bg-[var(--color-company)] px-4 py-3 text-center text-base font-medium text-[var(--dark-text)] transition duration-200 hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50`}
            >
              <Image
                src="/auth/people.svg"
                alt="login icon"
                width={15}
                height={15}
                className="h-auto w-4 space-x-3"
              />
              {!isSubmitting ? " Sign Up" : "Submitting..."}
            </button>
          </form>

          <div className="mt-6 flex w-full flex-col items-center justify-center text-center">
            <span className="mb-2 text-base font-normal text-[var(--dark-text-muted)]">
              OR,
            </span>

            <motion.button
              initial={{ width: 40 }}
              animate={{
                width: isHovered ? 402 : 40,
                padding: isHovered ? "0 8px" : "0",
                scale: isHovered ? 1.05 : 1,
                borderRadius: isHovered ? "5px" : "50%",
              }}
              whileTap={{ scale: 0.95 }}
              transition={{
                width: {
                  type: "spring",
                  duration: 2,
                  stiffness: 260,
                  damping: 20,
                  mass: 0.5,
                  ease: "linear",
                },
                duration: 0.9,
                ease: "linear",
                type: "spring",
                stiffness: 260,
                damping: 20,
                mass: 0.5,
              }}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              className="relative flex h-10 items-center space-x-4 overflow-hidden bg-[#EDEDED] shadow-md transition-shadow duration-300 hover:shadow-lg"
              aria-label="Continue with Google"
            >
              {/* Google Icon */}
              <div className="ml-2 flex h-[32] w-[32] flex-shrink-0 items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 533.5 544.3"
                  width="24"
                  height="24"
                >
                  <path
                    fill="#4285F4"
                    d="M533.5 278.4c0-18.3-1.6-36-4.6-53H272v100h147.1c-6.4 34.4-25.5 63.6-54.6 83.1v68h88c51.5-47.5 80-117.7 80-198.1z"
                  />
                  <path
                    fill="#34A853"
                    d="M272 544.3c73.8 0 135.8-24.5 181.1-66.6l-88-68c-24.5 16.4-55.8 26-93.1 26-71 0-131.2-47.9-152.7-112.2H30.3v70.7c45.3 89.5 138.8 149.1 241.7 149.1z"
                  />
                  <path
                    fill="#FBBC05"
                    d="M119.3 323.5c-10.2-30.2-10.2-62.8 0-93.1V159.7H30.3c-42.4 84.8-42.4 183.3 0 268.1l89-70.6z"
                  />
                  <path
                    fill="#EA4335"
                    d="M272 107.7c39.9-.6 78.2 14.4 107.5 41.8l80.2-80.2C418.2 24.5 356.2 0 272 0 169.1 0 75.6 59.6 30.3 149.1l89 70.6C140.8 155.6 201 107.7 272 107.7z"
                  />
                </svg>
              </div>

              {/* Text that appears on hover */}
              <motion.span
                className="mr-2 overflow-hidden text-[16px] font-[400] whitespace-nowrap text-[#A0A0A0]"
                initial={{ opacity: 0, width: 0 }}
                animate={{
                  opacity: isHovered ? 1 : 0,
                  width: isHovered ? "auto" : 0,
                }}
                transition={{
                  duration: 0.4,

                  ease: "easeOut",
                }}
              >
                Continue with Google
              </motion.span>
            </motion.button>

            <p className="mt-4 text-sm font-medium text-[var(--dark-text)]">
              Already have a account?{" "}
              <Link href="/login">
                <span className="text-[var(--color-company)]">Login</span>
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
