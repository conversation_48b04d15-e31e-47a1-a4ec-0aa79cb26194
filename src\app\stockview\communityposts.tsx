import React from "react";
import { <PERSON>, MessageSquare, Share2, ThumbsUp } from "lucide-react";
import Image from "next/image";
import { CommunityPostsProps } from "@/types/stockdetails";
import { Swiper, SwiperSlide } from "swiper/react";
import { <PERSON>M<PERSON>, Mousewheel } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/free-mode";

const CommunityPosts = () => {
  //   Community Posts Data Lists
  const CommunityPostsData = [
    {
      user: "/stockview/user1.jpg",
      name: "<PERSON><PERSON>",
      time: "2 hours ago",
      description:
        "NABIL showing strong support at 1220 levels. The recent dividend announcement should push this higher.",
    },
    {
      user: "/stockview/user2.jpg",
      name: "<PERSON><PERSON><PERSON>",
      time: "1 hours ago",
      description:
        "Quarterly results exceed expectations. Management guidance looks positive for next two quarters.",
    },
    {
      user: "/stockview/user3.jpg",
      name: "<PERSON><PERSON>",
      time: "2 hours ago",
      description:
        "Anyone else concerned about the banking sector liquidity? NABIL seems better positioned than peers though.",
    },
    {
      user: "/stockview/user4.jpg",
      name: "<PERSON><PERSON><PERSON>",
      time: "1 hours ago",
      description:
        "I’m long on NABIL since 1170. Planning to add more if it consolidates near support. Positive vibes on their quarterly!",
    },
    {
      user: "/stockview/3.jpg",
      name: "Anish Sharma",
      time: "1 hours ago",
      description:
        "Quarterly results exceed expectations. Management guidance looks positive for next two quarters.",
    },
    {
      user: "/stockview/user4.jpg",
      name: "Anish Sharma",
      time: "4 hours ago",
      description:
        "NABIL showing strong support at 1220 levels. The recent dividend announcement should push this higher.",
    },
  ];

  return (
    <Swiper
      modules={[FreeMode, Mousewheel]}
      spaceBetween={24}
      slidesPerView="auto"
      freeMode={true}
      mousewheel={{
        forceToAxis: true,
        sensitivity: 1,
        releaseOnEdges: true,
      }}
      grabCursor={true}
      className="community-posts-swiper"
    >
      {CommunityPostsData.map((item, idx) => (
        <SwiperSlide key={idx} style={{ width: "auto" }}>
          <CommunityPostsCard
            avatar={item.user}
            name={item.name}
            timestamp={item.time}
            content={item.description}
          />
        </SwiperSlide>
      ))}
    </Swiper>
  );
};

export default CommunityPosts;

export const CommunityPostsCard = ({
  avatar,
  name,
  timestamp,
  content,
}: CommunityPostsProps) => {
  return (
    <div className="flex w-full max-w-[289px] flex-col gap-6 rounded-md border border-[var(--dark-border)] bg-[var(--dark-surface)] p-3">
      {/* Top Section: Tag and Timestamp */}
      <div className="flex items-center gap-3">
        <div className="h-12 w-12 overflow-hidden rounded-full">
          <Image
            src={avatar}
            alt={name}
            width={40}
            height={40}
            className="object-content h-auto w-full"
          />
        </div>
        <p className="text-sm text-[var(--dark-text)]">
          Anish Sharma
          <span className="flex items-center gap-1 text-sm text-[var(--dark-text-muted)]">
            <Clock size={16} />
            {timestamp}
          </span>
        </p>
      </div>

      {/* Main Content */}
      <div className="flex-grow">
        <p className="mt-2 line-clamp-3 text-base font-normal text-[var(--dark-text)]">
          {content}
        </p>
      </div>

      {/* Footer Section: Source and Read More Link */}
      <div className="flex items-center gap-3 border-t border-[var(--dark-border)] p-3">
        {/* Like Container */}
        <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
          <ThumbsUp className="h-5 w-5" /> <span className="text-sm">48</span>
        </div>

        {/* Comments Container */}
        <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
          <MessageSquare className="h-5 w-5" />{" "}
          <span className="text-sm">48</span>
        </div>

        {/* Share Container */}
        <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
          <Share2 className="h-5 w-5" /> <span className="text-sm">48</span>
        </div>
      </div>
    </div>
  );
};
