import React from "react";
import { Clock, MessageSquare, Share2, ThumbsUp } from "lucide-react";
import Image from "next/image";
import { CommunityPostsProps } from "@/types/stockdetails";

const CommunityPosts = ({
  avatar,
  name,
  timestamp,
  content,
}: CommunityPostsProps) => {
  //   Community Posts Data Lists

  return (
    <div className="flex w-full max-w-[289px] flex-col gap-6 rounded-md border border-[var(--dark-border)] bg-[var(--dark-surface)] p-3">
      {/* Top Section: Tag and Timestamp */}
      <div className="flex items-center gap-3">
        <div className="h-12 w-12 overflow-hidden rounded-full">
          <Image
            src={avatar}
            alt={name}
            width={40}
            height={40}
            className="object-content h-auto w-full"
          />
        </div>
        <p className="text-sm text-[var(--dark-text)]">
          <PERSON><PERSON>
          <span className="flex items-center gap-1 text-sm text-[var(--dark-text-muted)]">
            <Clock size={16} />
            {timestamp}
          </span>
        </p>
      </div>

      {/* Main Content */}
      <div className="flex-grow">
        <p className="mt-2 line-clamp-3 text-base font-normal text-[var(--dark-text)]">
          {content}
        </p>
      </div>

      {/* Footer Section: Source and Read More Link */}
      <div className="flex items-center gap-3 border-t border-[var(--dark-border)] p-3">
        {/* Like Container */}
        <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
          <ThumbsUp className="h-5 w-5" /> <span className="text-sm">48</span>
        </div>

        {/* Comments Container */}
        <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
          <MessageSquare className="h-5 w-5" />{" "}
          <span className="text-sm">48</span>
        </div>

        {/* Share Container */}
        <div className="flex items-center gap-1 text-[var(--dark-text-muted)]">
          <Share2 className="h-5 w-5" /> <span className="text-sm">48</span>
        </div>
      </div>
    </div>
  );
};

export default CommunityPosts;
