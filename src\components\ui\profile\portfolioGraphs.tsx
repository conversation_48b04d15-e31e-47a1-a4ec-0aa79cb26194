import Image from "next/image";
import { IoBarChart } from "react-icons/io5";

export const SectorAllocation = () => {
  return (
    <div className="rounded-lg bg-[var(--dark-surface)] p-4">
      <header className="flex gap-2">
        <Image
          src="/profile/sector-allocation.svg"
          alt="Sector Allocation Icon"
          height={20}
          width={20}
        />
        <h5 className="text-base font-medium text-[var(--dark-text)]">
          Sector Allocation
        </h5>
      </header>

      {/* Main Pie Chart Data */}
      <div className="mt-4">
        <div className="flex items-center justify-center">
          <Image
            src="/profile/pie-chart.svg"
            alt="Sector Allocation Pie Chart"
            height={300}
            width={400}
          />
        </div>
      </div>
    </div>
  );
};

export const PortfolioValue = () => {
  return (
    <div className="rounded-lg bg-[var(--dark-surface)] p-4">
      <header className="flex gap-2">
        <Image
          src="/profile/portfolio-value.svg"
          alt="Portfolio Value Icon"
          height={20}
          width={20}
        />
        <h5 className="text-base font-medium text-[var(--dark-text)]">
          Portfolio Value
        </h5>
      </header>

      {/* Main Pie Chart Data */}
      <div className="mt-4">
        <div className="flex items-center justify-center">
          <Image
            src="/profile/portfolio-chart.svg"
            alt="Portfolio Value Pie Chart"
            height={300}
            width={400}
          />
        </div>
      </div>
    </div>
  );
};

export const TopGainersLosers = () => {
  return (
    <div className="rounded-lg bg-[var(--dark-surface)] p-4">
      <header className="flex gap-2">
        <IoBarChart className="text-success h-4 w-4" />
        <h5 className="text-base font-medium text-[var(--dark-text)]">
          Top Gainers & Losers
        </h5>
      </header>

      {/* Main Pie Chart Data */}
      <div className="mt-4">
        <div className="flex items-center justify-center px-5 py-3.5">
          <Image
            src="/profile/top-gainer-looser.svg"
            alt="Portfolio Value Pie Chart"
            height={250}
            width={845}
            className="h-auto w-full"
          />
        </div>
      </div>
    </div>
  );
};

export const PortfolioAchievements = () => {
  const achievementsList = [
    {
      title: "Achievements",
      description: "Achieved 20% portfolio growth in a single quarter",
      date: "Poush 2082",
      icon: "/profile/award.svg",
    },
    {
      title: "Achievements",
      description: "Achieved 20% portfolio growth in a single quarter",
      date: "Kartik 2082",
      icon: "/profile/network.svg",
    },
    {
      title: "Risk Manager",
      description: "Maintained positive returns during market downturn",
      date: "Bhadra 2082",
      icon: "/profile/risk-management.svg",
    },
    {
      title: "Community Leader",
      description: "Helped 5+ community members with portfolio advice",
      date: "Shrawn 2082",
      icon: "/profile/badge.svg",
    },
  ];
  return (
    <div className="mt-6 mb-4 rounded-lg bg-[var(--dark-surface)] p-4">
      <header className="flex items-center justify-between gap-2">
        <h5 className="text-base font-medium text-[var(--dark-text)]">
          Achievements
        </h5>
        <button className="text-sm font-normal text-[var(--dark-text)]">
          View All
        </button>
      </header>

      <div className="mt-4 grid grid-cols-1 gap-4 space-y-3 md:grid-cols-2">
        {achievementsList.map((achievement, idx) => (
          <div
            key={idx}
            className="flex items-center gap-3 rounded-lg bg-[var(--dark-border)] p-4"
          >
            <div className="flex h-11 w-11 items-center justify-center rounded-full bg-gradient-to-b from-[#1A7EF2] to-[#6366F1]">
              <Image
                src={achievement.icon}
                alt={achievement.title}
                height={24}
                width={24}
              />
            </div>
            <div>
              <h6 className="text-sm font-semibold text-[var(--dark-text)]">
                {achievement.title}
              </h6>
              <p className="text-xs text-[var(--dark-text-muted)]">
                {achievement.description}
              </p>
              <span className="text-xs text-[var(--dark-text-muted)]">
                {achievement.date}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
  return (
    <div className="mt-6 rounded-lg bg-[var(--dark-surface)] p-4">
      <header className="flex items-center justify-between gap-2">
        <h5 className="text-base font-medium text-[var(--dark-text)]">
          Achivements
        </h5>
        <button className="text-sm font-normal text-[var(--dark-text)]">
          View All
        </button>
      </header>

      {/* Main Pie Chart Data */}
      <div className="mt-4">
        <div className="flex items-center justify-center px-5 py-3.5"></div>
      </div>
    </div>
  );
};
