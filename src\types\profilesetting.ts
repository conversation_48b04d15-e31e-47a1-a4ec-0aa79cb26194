import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

export const settingProfileSchema = z.object({
    fullName: z.string().nonempty("Full name is required"),
    email: z
        .string()
        .nonempty("email is required")
        .refine((value) => {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            const phoneRegex = /^\d{10}$/;
            return emailRegex.test(value) || phoneRegex.test(value);
        }, "Please enter a valid email address or 10-digit phone number"),

    userName: z.string().nonempty("User name is required"),
});

export type Inputs = z.infer<typeof settingProfileSchema>;

export const settingProfileResolver = zodResolver(settingProfileSchema);
