@import "tailwindcss";

body {
  font-family: var(--font-poppins), sans-serif;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}
@theme {
  /* Dark Color Themes */
  --dark-bg: linear-gradient(to bottom, #000000, #111827);
  --dark-surface: #111827cc;
  --dark-border: #1f2937;
  --dark-text: #ededed;

  --dark-text-muted: #a0a0a0;

  /* Light Color Themes */
  --light-bg: #fffafa;
  --light-surface: #d6d6d6;
  --light-border: #c9c9c9;
  --light-text: #1a1a1a;
  --light-text-muted: #777777;

  /* Normal Colors */
  --color-company: #1a7ef2;
  --color-success: #16c784;
  --color-error: #ea3943;
  --color-warning: #f6c244;

  --super-gradientdata:
    linear-gradient(0deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.3)),
    linear-gradient(0deg, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8));
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Swiper Community Posts Styles */
.community-posts-swiper {
  overflow: hidden !important;
  width: 100% !important;
  max-width: 100% !important;
  contain: layout !important;
}

.community-posts-swiper .swiper-wrapper {
  width: auto !important;
  box-sizing: border-box !important;
}

.community-posts-swiper .swiper-slide {
  width: auto !important;
  flex-shrink: 0;
  max-width: 289px !important;
  box-sizing: border-box !important;
}
