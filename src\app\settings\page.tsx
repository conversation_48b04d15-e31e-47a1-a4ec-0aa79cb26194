"use client";
import Toggle from "@/components/ui/common/liquidtoggle";
import { Container } from "@/components/ui/common/container";
import { Inputs, settingProfileResolver } from "@/types/profilesetting";
import { AtSign } from "lucide-react";
import Image from "next/image";
import { useForm } from "react-hook-form";

const Settings = () => {
  // React Form Hooks
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors, isSubmitting },
  } = useForm<Inputs>({
    resolver: settingProfileResolver,
    defaultValues: {
      fullName: "Raju <PERSON> Ram",
      email: "<EMAIL>",
      userName: "Rajurajaram123",
    },
  });

  // Validatation for react hooks using ZOD
  const onSubmit = async (data: Inputs) => {
    try {
      await new Promise((resolve) => setTimeout(resolve, 5000));
      console.log("data:", data);
      reset();
    } catch (error) {
      console.error("Login failed:", error);
    }
  };

  // Helper function to get error message
  const getErrorMessage = (fieldName: keyof Inputs) => {
    return errors[fieldName]?.message;
  };
  return (
    <Container className="mt-24 mb-16 min-h-screen w-[950px] max-w-[970px] overflow-y-hidden rounded-xl border border-[var(--dark-border)] bg-gradient-to-b from-black to-gray-900 p-6 pt-4 backdrop-blur-md md:w-[970px]">
      <div className="rounded-lg">
        <h4 className="mb-2 text-3xl font-bold text-[var(--color-company)]">
          Settings
        </h4>
        <p className="text-base leading-relaxed font-normal text-[var(--dark-text-muted)]">
          Manage your preferences and account
        </p>
      </div>

      {/* Profile Settings */}
      <div className="w-[906px] p-6 pt-10 text-[var(--dark-text)]">
        <h5 className="flex items-center gap-1 text-base font-medium">
          <Image src="/user.svg" alt="User Icon" width={20} height={20} />
          Profile Setting
        </h5>
        {/* Placeholder for profile image content */}
        <div className="mt-5 flex items-center gap-6">
          <Image
            src="/settings/userprofile.svg"
            alt="User Icon"
            width={96}
            height={96}
            className="h-auto w-auto"
          />
          <button
            onClick={() => alert("Change Photo Clicked")}
            type="button"
            className="mt-2 rounded-xl bg-[var(--dark-border)] px-5 py-2.5 text-xs font-normal text-[#1A7EF2]"
          >
            Change Photo
          </button>
        </div>

        {/* Form for users Information */}
        <div className="mt-6">
          <form className="space-y-3" onSubmit={handleSubmit(onSubmit)}>
            {/* Full Name */}
            <div className="flex flex-col gap-1.5">
              <label
                htmlFor="fullName"
                className="text-sm font-normal text-[var(--dark-text-muted)]"
              >
                Full Name
              </label>

              <input
                value={watch("fullName")}
                suppressHydrationWarning
                {...register("fullName")}
                type="text"
                placeholder="Enter your full name"
                className={`w-full rounded-lg border px-4 py-3 pr-4 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                  errors.fullName
                    ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                    : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                }`}
              />

              {errors.fullName && (
                <p className="mt-1 text-xs text-[var(--color-error)]">
                  {getErrorMessage("fullName")}
                </p>
              )}
            </div>

            {/* UserName */}
            <div className="flex flex-col gap-1.5">
              <label
                htmlFor="userName"
                className="text-sm font-normal text-[var(--dark-text-muted)]"
              >
                User Name
              </label>
              <div className="relative">
                <AtSign className="absolute top-1/2 left-3 h-[20px] w-[20px] -translate-y-1/2 transform text-white" />
                <input
                  value={watch("userName")}
                  suppressHydrationWarning
                  {...register("userName")}
                  type="text"
                  placeholder="Enter your user name"
                  className={`w-full rounded-lg border px-4 py-3 pr-4 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                    errors.userName
                      ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                      : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                  }`}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-xs text-[var(--color-error)]">
                  {getErrorMessage("userName")}
                </p>
              )}
            </div>

            {/* Email or Phone */}
            <div className="flex flex-col gap-1.5">
              <label
                htmlFor="email"
                className="text-sm font-normal text-[var(--dark-text-muted)]"
              >
                Email or Phone Number
              </label>
              <div className="relative">
                <Image
                  src="/auth/email.svg"
                  alt="email icon"
                  width={20}
                  height={20}
                  className="absolute top-1/2 left-3 -translate-y-1/2 transform text-white"
                />
                <input
                  value={watch("email")}
                  suppressHydrationWarning
                  {...register("email")}
                  type="email"
                  placeholder="Enter your email or phone"
                  className={`w-full rounded-lg border px-4 py-3 pr-4 pl-10 text-[var(--dark-text-muted)] focus:ring-1 focus:outline-none ${
                    errors.email
                      ? "border-[var(--color-error)] focus:border-red-500 focus:ring-red-500"
                      : "border-[var(--dark-border)] focus:border-blue-500 focus:ring-blue-500"
                  }`}
                />
              </div>
              {errors.email && (
                <p className="mt-1 text-xs text-[var(--color-error)]">
                  {getErrorMessage("email")}
                </p>
              )}
            </div>
          </form>
        </div>
      </div>

      {/* Community Preferences  */}
      <Container className="mt-10 w-[906px] border-t border-[var(--dark-border)] p-6 pt-4 text-[var(--dark-text)]">
        <h5 className="flex items-center gap-3 text-base font-medium text-[var(--dark-text)]">
          <Image src="/settings/globe.svg" alt="globe" height={20} width={20} />
          Community Preferences
        </h5>

        <div className="mt-6 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Show High Achiever Badge
            </h3>
            <Toggle />
          </div>

          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Allow Comments on Posts
            </h3>
            <Toggle />
          </div>

          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Show Watchlist to Others
            </h3>
            <Toggle />
          </div>
        </div>
      </Container>

      {/* Interface Preferences */}
      <Container className="mt-10 w-[906px] border-t border-[var(--dark-border)] p-6 pt-4 text-[var(--dark-text)]">
        <h5 className="flex items-center gap-3 text-base font-medium text-[var(--dark-text)]">
          <Image
            src="/settings/interface.svg"
            alt="globe"
            height={20}
            width={20}
          />
          Interface Preferences
        </h5>

        <div className="mt-6 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Dark Mode
            </h3>
            <Toggle />
          </div>

          <div className="">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Font Size
            </h3>

            <div className="mt-5 flex items-center gap-2">
              <button className="h-[32px] w-[79px] rounded-2xl bg-[#2182F299]/60 px-2.5 py-1 text-base font-normal text-[var(--dark-text)]">
                Normal
              </button>
              <button className="h-[32px] w-[79px] rounded-2xl bg-[var(--dark-border)] px-2.5 py-1 text-base font-normal text-[var(--dark-text)]">
                Large
              </button>
            </div>
          </div>
        </div>
      </Container>

      {/* Notifications Preferences */}
      <Container className="mt-10 w-[906px] border-t border-[var(--dark-border)] p-6 pt-4 text-[var(--dark-text)]">
        <h5 className="flex items-center gap-3 text-base font-medium text-[var(--dark-text)]">
          <Image
            src="/settings/notification.svg"
            alt="globe"
            height={20}
            width={20}
          />
          Notification Preferences
        </h5>

        <div className="mt-6 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Email Course Updates
            </h3>
            <Toggle />
          </div>

          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Market Alerts{" "}
              <span className="ml-2 rounded-4xl bg-[#374151] px-1 py-0.5 !text-xs !font-normal">
                Coming Soon
              </span>
            </h3>
            <Toggle />
          </div>
        </div>
      </Container>

      {/* Security Preferences */}
      <Container className="mt-10 w-[906px] border-t border-[var(--dark-border)] p-6 pt-4 text-[var(--dark-text)]">
        <h5 className="flex items-center gap-3 text-base font-medium text-[var(--dark-text)]">
          <Image
            src="/settings/security.svg"
            alt="globe"
            height={20}
            width={20}
          />
          Security
        </h5>

        <div className="mt-6 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-normal text-[var(--dark-text]">
              Enable Two-Factor Authentication
            </h3>
            <Toggle />
          </div>

          <div className="space-y-3">
            <button className="flex w-full items-center gap-2 rounded-lg border border-[var(--dark-border)] bg-[#1F2937] p-3">
              <Image
                src="/common/lock.svg"
                alt="globe"
                height={16}
                width={16}
              />
              <h3 className="text-sm font-normal">Change Password</h3>
            </button>
            <button className="flex w-full items-center gap-2 rounded-lg border border-[var(--dark-border)] bg-[#1F2937] p-3">
              <Image
                src="/common/logout.svg"
                alt="globe"
                height={16}
                width={16}
              />
              <h3 className="text-sm font-normal">Logout</h3>
            </button>
          </div>
        </div>
      </Container>

      {/* Danger Zone */}
      <Container className="mt-10 w-[906px] border-t border-[var(--dark-border)] p-6 pt-4 text-[var(--dark-text)]">
        <h5 className="flex items-center gap-3 text-base font-medium text-[#E5000E]">
          Danger Zone
        </h5>

        <div className="mt-6 flex flex-col gap-3">
          <button className="flex w-full items-center gap-2 rounded-lg border border-[var(--dark-border)] bg-[#311012] p-3">
            <Image src="/settings/bin.svg" alt="globe" height={16} width={16} />
            <h3 className="text-sm font-normal text-[#FCA5A5]">
              Delete Account
            </h3>
          </button>

          <button className="flex w-full items-center gap-2 rounded-lg border border-[var(--dark-border)] bg-[#311012] p-3">
            <Image
              src="/settings/data.svg"
              alt="globe"
              height={16}
              width={16}
            />
            <h3 className="text-sm font-normal text-[#FCA5A5]">
              Clear Portfolio Data
            </h3>
          </button>
          <p className="text-xs font-normal text-[var(--dark-text-muted)]">
            Please think carefully before clicking—these actions may be
            irreversible.{" "}
          </p>
        </div>
      </Container>
    </Container>
  );
};

export default Settings;
