"use client";

import { useEffect, useState } from "react";
import { motion } from "motion/react";

// Typing animation component
import { TypingAnimation } from "@/components/ui/common/typingtextanimation";

const ContentAnimation = () => {
  const [currentQuote, setCurrentQuote] = useState<{
    title: string;
    author: string;
  } | null>(null);
  const [quoteIndex, setQuoteIndex] = useState<number>(0);

  //  quotes dummy data
  const quotes = [
    {
      title:
        "Success is not final, failure is not fatal: It is the courage to continue that counts.",
      author: "- <PERSON>",
    },
    {
      title: "Be yourself; everyone else is already taken.",
      author: "- <PERSON>",
    },
    {
      title:
        "Do not go where the path may lead, go instead where there is no path and leave a trail.",
      author: "- <PERSON>",
    },
    {
      title:
        "The only limit to our realization of tomorrow is our doubts of today.",
      author: "- <PERSON>",
    },
    {
      title: "In the middle of every difficulty lies opportunity.",
      author: "- <PERSON>",
    },
    {
      title: "It does not matter how slowly you go as long as you do not stop.",
      author: "- <PERSON>fu<PERSON>",
    },
    {
      title:
        "What lies behind us and what lies before us are tiny matters compared to what lies within us.",
      author: "- <PERSON>",
    },
    {
      title:
        "Happiness is not something ready made. It comes from your own actions.",
      author: "- <PERSON>ai <PERSON>",
    },
    {
      title: "If you want to lift yourself up, lift up someone else.",
      author: "- <PERSON> T. <PERSON>",
    },
    {
      title: "Believe you can and you're halfway there.",
      author: "- <PERSON> <PERSON>",
    },
  ];

  // Initialize with first quote
  useEffect(() => {
    setCurrentQuote(quotes[0]);
  }, []);

  // Auto change quotes every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setQuoteIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % quotes.length;
        setCurrentQuote(quotes[nextIndex]);
        return nextIndex;
      });
    }, 5000); // Change quote every 5 seconds

    return () => clearInterval(interval);
  }, [quotes]);

  return (
    <div className="mb-5 hidden flex-1 px-2 text-center tracking-tight text-[var(--dark-text)] md:block md:text-start">
      <h1 className="text-4xl font-semibold lg:mb-4">Good to see you !</h1>
      <p className="text-medium text-xl">
        Let's get you back to trading smarter and faster.
      </p>
      <div className="min-h-[80px] w-fit pt-8 text-base font-normal text-[var(--dark-text)]">
        {currentQuote && (
          <motion.div
            key={quoteIndex}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <TypingAnimation text={currentQuote.title} delay={300} speed={25} />
            <br />
            <span className="block text-end">
              <TypingAnimation
                text={currentQuote.author}
                delay={currentQuote.title.length * 25 + 800}
                speed={50}
              />
            </span>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default ContentAnimation;
